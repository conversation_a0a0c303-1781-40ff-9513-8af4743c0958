import { http } from "@/utils/http";

/** 优惠券保存 */
export const couponSave = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/coupon/save",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || "创建了优惠券"
      }
    }
  );
};

/** 优惠券分页查询 */
export const couponFindAll = params => {
  return http.request(
    "get",
    "/platform/coupon/findAll",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 根据ID查询优惠券详情 */
export const couponFindById = params => {
  return http.request(
    "get",
    "/platform/coupon/findById",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

/** 优惠券状态切换 */
export const couponToggleStatus = (data, operateLog = {}) => {
  return http.request(
    "post",
    "/platform/coupon/enabled",
    { data },
    {
      isNeedToken: true,
      isNeedEncrypt: true,
      operateLog: {
        operateLogType: operateLog?.operateLogType || "COUPON_MANAGEMENT",
        additionalParameter: operateLog?.additionalParameter || "",
        operatorTarget: operateLog?.operatorTarget || "",
        operateType: operateLog?.operateType || ""
      }
    }
  );
};

/** 查询优惠券领取记录 */
export const findReceiveRecordByCouponId = params => {
  return http.request(
    "get",
    "/platform/coupon/findReceiveRecordByCouponId",
    { params },
    { isNeedToken: true, isNeedEncrypt: true }
  );
};

// 根据课期Id查询优惠券-分页
export const findCouponPageByCoursePeriodId = params => {
  return http.request(
    "get",
    "/platform/priceSetting/findCouponPageByCoursePeriodId",
    { params },
    { isNeedEncrypt: true, isNeedToken: true }
  );
};
