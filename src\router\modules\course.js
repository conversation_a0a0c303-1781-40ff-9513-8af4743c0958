import { $t } from "@/plugins/i18n";
import { course } from "@/router/enums.js";
import CourseIcon from "@/assets/home/<USER>";
import CourseIconActive from "@/assets/home/<USER>";
import { coCodesList, reCodesList } from "@/router/accidCode.js";

export default {
  path: "/course",
  redirect: "/course/management",
  meta: {
    icon: "ri:information-line",
    imgIcon: CourseIcon,
    imgIconActive: CourseIconActive,
    // showLink: false,
    title: "课程",
    rank: course,
    idCode: coCodesList.baseCode
  },
  children: [
    {
      path: "/course/management",
      name: "courseManagement",
      redirect: "/course/management/index",
      // component: () => import("@/views/course/courseManagement/index.vue"),
      meta: {
        title: "课程管理"
        // idCode: coCodesList.course
      },
      children: [
        {
          path: "/course/management/index",
          name: "CourseManagementIndex",
          component: () => import("@/views/course/courseManagement/index.vue"),
          meta: {
            title: "课程管理",
            idCode: coCodesList.course,
            keepAlive: true
          }
        },
        {
          path: "/course/management/detail",
          name: "CourseManagementDetail",
          component: () =>
            import("@/views/course/courseManagement/courseDetail.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "课程详情",
            idCode: reCodesList.courseManagementDetail,
            showLink: false,
            keepAlive: true
          },
          children: [
            {
              path: "/course/management/current/details",
              name: "courseManagementCurrentDetails",
              component: () =>
                import("@/views/course/courseManagement/currentDetails.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "当期详情",
                idCode: reCodesList.courseManagementCurrentDetails,
                showLink: false
              },
              children: [
                {
                  path: "/course/orderManagement/orderDetail",
                  name: "courseOrderDetail",
                  component: () =>
                    import("@/views/course/orderManagement/orderDetails.vue"),
                  meta: {
                    title: "订单详情",
                    idCode: reCodesList.courseOrderDetails,
                    showLink: false
                  }
                }
              ]
            },
            {
              path: "/course/management/information",
              name: "courseManagementInformation",
              component: () =>
                import("@/views/course/courseManagement/detailInformation.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "详细资料",
                idCode: reCodesList.courseManagementInformation,
                showLink: false
              }
            }
          ]
        },
        {
          path: "/course/management/allEvaluate",
          name: "allEvaluate",
          component: () =>
            import("@/views/course/courseManagement/allEvaluate.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "用户评价",
            idCode: reCodesList.allEvaluate,
            showLink: false
          },
          children: [
            {
              path: "/course/evaluateDetail",
              name: "evaluateDetail",
              component: () =>
                import("@/views/course/courseManagement/evaluateDetail.vue"),
              meta: {
                title: "评价详情",
                idCode: reCodesList.evaluateDetail
              }
            }
          ]
        }
      ]
    },
    {
      path: "/classification/management",
      name: "ClassificationManagement",
      component: () =>
        import("@/views/course/classificationManagement/index.vue"),
      meta: {
        title: "分类管理",
        keepAlive: true,
        idCode: coCodesList.classification
      }
    },
    {
      path: "/course/examine",
      name: "courseExamine",
      // component: () => import("@/views/course/courseExamine/index.vue"),
      redirect: "/course/examine/index",
      meta: {
        title: "课程审核",
        idCode: coCodesList.examine
      },
      children: [
        {
          path: "/course/examine/index",
          name: "CourseExamineIndex",
          component: () => import("@/views/course/courseExamine/index.vue"),
          meta: {
            title: "课程审核",
            keepAlive: true,
            idCode: coCodesList.examine
          }
        },
        {
          path: "/course/examine/detail",
          name: "courseExamineDetail",
          component: () => import("@/views/course/courseExamine/detail.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "审核详情",
            idCode: reCodesList.courseExamineDetail,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/order/managementa",
      name: "orderManagementa",
      redirect: "/order/management",
      // component: () => import("@/views/course/orderManagement/index.vue"),
      meta: {
        title: "订单管理"
        // idCode: coCodesList.order
      },
      children: [
        {
          path: "/order/management",
          name: "orderManagement",
          component: () => import("@/views/course/orderManagement/index.vue"),
          meta: {
            title: "订单管理",
            idCode: coCodesList.order,
            keepAlive: true
          }
        },
        {
          path: "/course/orderManagement/orderDetails",
          name: "courseOrderDetails",
          component: () =>
            import("@/views/course/orderManagement/orderDetails.vue"),
          meta: {
            title: "订单详情",
            idCode: reCodesList.courseOrderDetails,
            showLink: false
          },
          children: [
            {
              path: "/course/management/current",
              name: "courseManagementCurrent",
              component: () =>
                import("@/views/course/courseManagement/currentDetails.vue"),
              meta: {
                // title: $t("menus.pureFourZeroOne")
                title: "当期课程详情",
                idCode: reCodesList.courseManagementCurrentDetails,
                showLink: false
              }
            }
          ]
        },
        {
          path: "/course/orderManagement/currentDetails",
          name: "orderManagementCurrentDetails",
          component: () =>
            import("@/views/course/courseManagement/currentDetails.vue"),
          meta: {
            title: "当期详情",
            idCode: reCodesList.courseManagementCurrentDetails,
            showLink: false
          },
          children: [
            {
              path: "/course/orderManagement/currentDetails/orderDetail",
              name: "orderCurrentOrderDetail",
              component: () =>
                import("@/views/course/orderManagement/orderDetails.vue"),
              meta: {
                title: "订单详情",
                idCode: reCodesList.courseOrderDetails,
                showLink: false
              }
            }
          ]
        }
      ]
    },
    // {
    //   path: "/course/market",
    //   name: "courseMarket",
    //   redirect: "/course/market/index",
    //   meta: {
    //     title: "课程市场"
    //   },
    //   children: [
    //     {
    //       path: "/course/market/index",
    //       name: "courseMarketIndex",
    //       component: () => import("@/views/course/courseMarket/index.vue"),
    //       meta: {
    //         title: "课程市场",
    //         idCode: coCodesList.courseMarket,
    //         keepAlive: true
    //       }
    //     },
    //     {
    //       path: "/course/market/detailInstitution",
    //       name: "courseMarketDetailInstitution",
    //       component: () =>
    //         import(
    //           "@/views/course/courseMarket/components/InstitutionReleaseDetails.vue"
    //         ),
    //       meta: {
    //         title: "机构发布详情",
    //         idCode: reCodesList.courseMarketDetailInstitution,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/detailFaculty",
    //       name: "courseMarketDetailFaculty",
    //       component: () =>
    //         import(
    //           "@/views/course/courseMarket/components/facultyReleaseDetails.vue"
    //         ),
    //       meta: {
    //         title: "师资发布详情",
    //         idCode: reCodesList.courseMarketDetailFaculty,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/detailRecruit",
    //       name: "courseMarketDetailRecruit",
    //       component: () =>
    //         import(
    //           "@/views/course/courseMarket/components/recruitmentDemandDetails.vue"
    //         ),
    //       meta: {
    //         title: "招募详情",
    //         idCode: reCodesList.courseMarketDetailRecruit,
    //         showLink: false
    //       }
    //     },
    //     {
    //       path: "/course/market/currentDetails",
    //       name: "courseMarketCurrentDetails",
    //       component: () =>
    //         import("@/views/course/courseManagement/currentDetails.vue"),
    //       meta: {
    //         title: "课程详情",
    //         idCode: reCodesList.courseMarketCurrentDetails,
    //         showLink: false
    //       }
    //     }
    //   ]
    // },
    {
      path: "/evaluate/management",
      name: "EvaluateManagement",
      // component: () => import("@/views/course/courseExamine/index.vue"),
      redirect: "/evaluate/management/index",
      meta: {
        title: "评价管理",
        idCode: coCodesList.evaluate
      },
      children: [
        {
          path: "/evaluate/management/index",
          name: "EvaluateManagementIndex",
          component: () =>
            import("@/views/course/evaluateManagement/index.vue"),
          meta: {
            title: "评价管理",
            keepAlive: true,
            idCode: coCodesList.evaluate
          }
          // children:[

          // ]
        },
        {
          path: "/course/current/details/evaluate",
          name: "courseCurrentDetailsEvaluate",
          component: () =>
            import("@/views/course/courseManagement/currentDetails.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "当期详情",
            idCode: reCodesList.courseManagementCurrentDetails,
            showLink: false
          },
          children: [
            {
              path: "/course/orderManagement/orderDetail",
              name: "courseOrderDetail",
              component: () =>
                import("@/views/course/orderManagement/orderDetails.vue"),
              meta: {
                title: "订单详情",
                idCode: reCodesList.courseOrderDetails,
                showLink: false
              }
            }
          ]
        },
        {
          path: "/course/examine/detail",
          name: "courseExamineDetail",
          component: () => import("@/views/course/courseExamine/detail.vue"),
          meta: {
            // title: $t("menus.pureFourZeroOne")
            title: "审核详情",
            idCode: reCodesList.courseExamineDetail,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/appeal/management",
      name: "AppealManagement",
      // component: () => import("@/views/course/courseExamine/index.vue"),
      redirect: "/appeal/management/index",
      meta: {
        title: "申诉管理",
        idCode: coCodesList.appeal
      },
      children: [
        {
          path: "/appeal/management/index",
          name: "AppealManagementIndex",
          component: () => import("@/views/course/appealManagement/index.vue"),
          meta: {
            title: "申诉管理",
            keepAlive: true,
            idCode: coCodesList.appeal
          }
        },
        {
          path: "/appeal/management/orderDetails",
          name: "OrderDetailsIndex",
          component: () =>
            import("@/views/course/orderManagement/orderDetails.vue"),
          meta: {
            title: "订单详情"
            // keepAlive: true,
            // idCode: coCodesList.appeal
          }
        },
        {
          path: "/appeal/management/courseDetail",
          name: "CourseDetailIndex",
          component: () =>
            import("@/views/course/courseManagement/currentDetails.vue"),
          meta: {
            title: "课程详情"
            // keepAlive: true,
            // idCode: coCodesList.appeal
          }
        }
      ]
    },
    {
      path: "/financial/management",
      name: "FinancialManagement",
      redirect: "/financial/management/index",
      meta: {
        title: "财务管理",
        idCode: coCodesList.financial
      },
      children: [
        {
          path: "/financial/management/index",
          name: "FinancialManagementIndex",
          component: () =>
            import("@/views/course/financialManagement/index.vue"),
          meta: {
            title: "财务管理",
            keepAlive: true,
            idCode: coCodesList.financial
          }
        },
        {
          path: "/financial/management/currentDetails",
          name: "financialManagementCurrentDetails",
          component: () =>
            import("@/views/course/courseManagement/currentDetails.vue"),
          meta: {
            title: "当期详情",
            idCode: reCodesList.courseManagementCurrentDetails,
            showLink: false
          }
        },
        {
          path: "/financial/management/currentDetails/orderDetail",
          name: "financialCurrentOrderDetail",
          component: () =>
            import("@/views/course/orderManagement/orderDetails.vue"),
          meta: {
            title: "订单详情",
            idCode: reCodesList.courseOrderDetails,
            showLink: false
          }
        }
      ]
    },
    {
      path: "/coupon/management",
      name: "CouponManagement",
      redirect: "/coupon/management/index",
      meta: {
        title: "优惠券管理",
        idCode: coCodesList.coupon
      },
      children: [
        {
          path: "/coupon/management/index",
          name: "CouponManagementIndex",
          component: () => import("@/views/course/couponManagement/index.vue"),
          meta: {
            title: "优惠券管理",
            keepAlive: true,
            idCode: coCodesList.coupon
          }
        },
        {
          path: "/coupon/management/create",
          name: "CouponManagementCreate",
          component: () => import("@/views/course/couponManagement/create.vue"),
          meta: {
            title: "新建优惠券",
            keepAlive: false,
            showLink: false,
            idCode: reCodesList.couponCreate
          }
        },
        {
          path: "/coupon/management/detail",
          name: "CouponManagementDetail",
          component: () => import("@/views/course/couponManagement/detail.vue"),
          meta: {
            title: "优惠券详情",
            keepAlive: false,
            showLink: false
            // idCode: reCodesList.couponDetail
          },
          children: [
            {
              path: "/coupon/management/orderDetail",
              name: "couponOrderDetail",
              component: () =>
                import("@/views/course/orderManagement/orderDetails.vue"),
              meta: {
                title: "订单详情",
                showLink: false
              }
            },
            {
              path: "/coupon/management/createCoupon",
              name: "CouponManagementCreateCoupon",
              component: () =>
                import("@/views/course/couponManagement/create.vue"),
              meta: {
                title: "新建优惠券",
                keepAlive: false,
                showLink: false,
                idCode: reCodesList.couponCreate
              }
            }
          ]
        }
      ]
    }
  ]
};
