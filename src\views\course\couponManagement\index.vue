<script setup>
import { ref, reactive, onMounted, onActivated, h } from "vue";
import { useRouter } from "vue-router";
import { PlusSearch } from "plus-pro-components";
import { ElMessage, ElMessageBox } from "element-plus";
import { couponFindAll, couponToggleStatus } from "@/api/coupon";
import { formatTime } from "@/utils/index";
import { requestTo } from "@/utils/http/tool";

defineOptions({
  name: "CouponManagementIndex"
});

const router = useRouter();

// 搜索表单数据
const searchForm = ref({
  name: "",
  distributionTimeRange: [],
  useTimeRange: [],
  enabled: ""
});

// Tab栏配置
const activeTab = ref(0);
const tabOptions = ref([
  { name: "有效", value: "valid" },
  { name: "已失效", value: "invalid" }
]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 搜索配置
const searchColumns = ref([
  {
    label: "优惠券名称",
    prop: "name"
  },
  {
    label: "发放时间",
    prop: "distributionTimeRange",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  {
    label: "使用时间",
    prop: "useTimeRange",
    valueType: "date-picker",
    fieldProps: {
      type: "daterange",
      startPlaceholder: "开始时间",
      endPlaceholder: "结束时间"
    }
  },
  {
    label: "状态",
    prop: "enabled",
    valueType: "select",
    options: [
      { label: "全部", value: "" },
      { label: "启用", value: true },
      { label: "停用", value: false }
    ]
  }
]);

// 基础表格列配置
const baseColumns = [
  {
    label: "优惠券名",
    prop: "name",
    minWidth: 120
  },
  {
    label: "发放时间（开始）",
    prop: "distributionStartTime",
    minWidth: 120,
    formatter: ({ distributionStartTime }) => {
      return distributionStartTime
        ? formatTime(distributionStartTime, "YYYY-MM-DD")
        : "--";
    }
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150,
    formatter: ({ startTime, endTime }) => {
      const start = startTime ? formatTime(startTime, "YYYY-MM-DD") : "";
      const end = endTime ? formatTime(endTime, "YYYY-MM-DD") : "";
      return start && end ? `${start} 至 ${end}` : "无限制";
    }
  },
  {
    label: "领取数量/发放数量",
    prop: "receiveCount",
    minWidth: 140,
    formatter: ({ receivedNumber, totalIssue }) => {
      const received = receivedNumber || 0;
      const total = totalIssue || 0;
      const percentage =
        total > 0
          ? (() => {
              const value = ((received / total) * 100).toFixed(2);
              return parseFloat(value) % 1 === 0 ? parseInt(value) : value;
            })()
          : 0;
      return `${received}/${total} (${percentage}%)`;
    }
  },
  {
    label: "优惠规则",
    prop: "discountRule",
    minWidth: 120,
    formatter: ({ couponDiscountType, discountAmount, conditionAmount }) => {
      if (couponDiscountType === "FULL_REDUCTION") {
        return `满${conditionAmount || 0}减${discountAmount || 0}`;
      } else if (couponDiscountType === "DISCOUNT") {
        return `${discountAmount || 0}折`;
      } else if (couponDiscountType === "FIXED") {
        return `立减${discountAmount || 0}`;
      }
      return "--";
    }
  }
];

// 状态颜色映射
const getStatusColor = enabled => {
  return enabled ? "#67C23A" : "#F56C6C"; // 启用：绿色，停用：红色
};

// 状态列配置
const statusColumn = {
  label: "状态",
  prop: "enabled",
  minWidth: 80,
  cellRenderer: ({ row }) => {
    const isEnabled = row.enabled;
    const statusText = isEnabled ? "启用" : "停用";
    return h(
      "div",
      {
        class: "status-text",
        style: { color: getStatusColor(isEnabled) }
      },
      statusText
    );
  }
};

// 操作列配置
const operationColumn = {
  label: "操作",
  fixed: "right",
  width: 260,
  slot: "operation"
};

// 动态表格列配置
const columns = ref([]);

// 判断优惠券是否有效
const isValidCoupon = coupon => {
  const now = new Date().getTime();
  const endTime = coupon.endTime;

  // 如果没有结束时间限制，认为是有效的
  if (!coupon.isUseLimit || !endTime) {
    return true;
  }

  // 如果当前时间小于结束时间，认为是有效的
  return now < endTime;
};

// 根据当前tab更新表格列配置
const updateColumns = () => {
  const currentTabValue = tabOptions.value[activeTab.value]?.value;
  const newColumns = [...baseColumns];

  // 如果是有效tab，显示状态列
  if (currentTabValue === "valid") {
    newColumns.push(statusColumn);
  }

  // 添加操作列
  newColumns.push(operationColumn);

  columns.value = newColumns;
};

// 构建API请求参数
const buildApiParams = () => {
  const currentTabValue = tabOptions.value[activeTab.value]?.value;
  const params = {
    timeStamp: new Date().getTime(),
    page: pagination.currentPage - 1, // API从0开始
    size: pagination.pageSize,
    valid: currentTabValue, // 'valid' 或 'invalid'
    sort: "id,desc"
  };

  // 添加搜索条件
  if (searchForm.value.name) {
    params.name = searchForm.value.name;
  }

  if (searchForm.value.enabled !== "") {
    params.enabled = searchForm.value.enabled;
  }

  // 处理发放时间范围
  if (
    searchForm.value.distributionTimeRange &&
    searchForm.value.distributionTimeRange.length === 2
  ) {
    params.distributionStartTime = new Date(
      searchForm.value.distributionTimeRange[0]
    ).getTime();
    params.distributionEndTime = new Date(
      searchForm.value.distributionTimeRange[1]
    ).getTime();
  }

  // 处理使用时间范围
  if (
    searchForm.value.useTimeRange &&
    searchForm.value.useTimeRange.length === 2
  ) {
    params.startTime = new Date(searchForm.value.useTimeRange[0]).getTime();
    params.endTime = new Date(searchForm.value.useTimeRange[1]).getTime();
  }

  return params;
};

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  try {
    const params = buildApiParams();
    const [err, result] = await requestTo(couponFindAll(params));

    if (err) {
      ElMessage.error("获取优惠券列表失败");
      return;
    }

    if (result) {
      console.log("result----------", result);

      tableData.value = result.content || [];
      pagination.total = result.totalElements || 0;
    } else {
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("加载优惠券数据失败:", error);
    ElMessage.error("获取优惠券列表失败");
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// Tab切换处理
const handleTabClick = tab => {
  activeTab.value = tab.props ? tab.props.name : tab.index;
  pagination.currentPage = 1;
  updateColumns(); // 更新表格列配置
  loadTableData();
};

// 领取地址相关数据
const qrCodeVisible = ref(false);
const currentCoupon = ref({});
const qrCodeUrl = ref("");

// 搜索处理
const handleSearch = values => {
  console.log("搜索参数:", values);
  searchForm.value = { ...values };
  pagination.currentPage = 1;
  loadTableData();
};

// 重置处理
const handleReset = () => {
  console.log("重置搜索");
  loadTableData();
};

// 分页处理
const handleSizeChange = size => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  loadTableData();
};

const handleCurrentChange = page => {
  pagination.currentPage = page;
  loadTableData();
};

// 新建优惠券
const handleCreateCoupon = () => {
  router.push({
    path: "/coupon/management/create",
    query: {
      type: "create"
    }
  });
};

// 操作处理函数
const handleDetail = row => {
  const currentTabValue = tabOptions.value[activeTab.value]?.value;
  router.push({
    path: "/coupon/management/detail",
    query: {
      id: row.id,
      valid: currentTabValue
    }
  });
};

const handleToggleStatus = async row => {
  const action = row.enabled ? "停用" : "启用";
  try {
    await ElMessageBox.confirm(
      `确定要${action}优惠券"${row.name}"吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    const operateLog = {
      operateLogType: "COUPON_MANAGEMENT",
      operatorTarget: `“${row.name}”`,
      operateType: `${action}了优惠券`
    };
    const params = {
      id: row.id,
      invalidate: !row.enabled
    };
    const [err, result] = await requestTo(
      couponToggleStatus(params, operateLog)
    );

    if (err) {
      ElMessage.error(`${action}失败`);
      return;
    }

    ElMessage.success(`${action}成功`);
    // 重新加载数据
    loadTableData();
  } catch (error) {
    if (error !== "cancel") {
      console.error("切换优惠券状态失败:", error);
      ElMessage.error(`${action}失败`);
    }
  }
};

const handleCopy = row => {
  // 这里应该调用接口获取优惠券详情，但后端未实现，所以直接跳转
  ElMessage.success(`正在复制优惠券: ${row.name}`);
  router.push({
    path: "/coupon/management/create",
    query: {
      type: "copy",
      id: row.id
    }
  });
};

// 显示领取地址二维码
const handleShowQRCode = row => {
  currentCoupon.value = row;
  qrCodeVisible.value = true;
  qrCodeUrl.value = row.qrCodeFileIdentifier; // 使用后端返回的二维码链接
  console.log("获取优惠券二维码:", row.qrCodeFileIdentifier);
};

// 初始化
onMounted(() => {
  updateColumns(); // 初始化表格列配置
  loadTableData();
});
onActivated(() => {
  updateColumns(); // 初始化表格列配置
  loadTableData();
});
</script>

<template>
  <div class="coupon-management">
    <!-- 搜索区域 -->
    <div class="common">
      <div class="search-section">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="6"
          :has-unfold="false"
          label-width="120px"
          @search="handleSearch"
          @reset="handleReset"
        />
      </div>
    </div>

    <!-- Tab栏和操作按钮 -->
    <div class="common">
      <!-- Tab栏和按钮在同一行 -->
      <div class="tab-button-row">
        <div class="coupon-tabs">
          <el-tabs
            v-model="activeTab"
            class="demo-tabs"
            @tab-click="handleTabClick"
          >
            <el-tab-pane
              v-for="(item, index) in tabOptions"
              :key="index"
              :label="item.name"
              :name="index"
            />
          </el-tabs>
        </div>

        <div class="button-section">
          <el-button type="primary" @click="handleCreateCoupon">
            新建优惠券
          </el-button>
        </div>
      </div>

      <div class="table-section">
        <pure-table
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="left"
          table-layout="auto"
          :loading="loading"
          :data="tableData"
          :columns="columns"
          :pagination="{ ...pagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row }">
            <div class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleDetail(row)"
              >
                详情
              </el-button>
              <!-- 有效优惠券显示所有操作按钮 -->
              <template v-if="tabOptions[activeTab]?.value === 'valid'">
                <el-button
                  class="reset-margin"
                  link
                  type="primary"
                  @click="handleShowQRCode(row)"
                >
                  领取地址
                </el-button>
                <el-button
                  class="reset-margin"
                  link
                  :type="row.enabled ? 'danger' : 'primary'"
                  @click="handleToggleStatus(row)"
                >
                  {{ row.enabled ? "停用" : "启用" }}
                </el-button>
              </template>
              <!-- 复制按钮对所有tab都显示 -->
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleCopy(row)"
              >
                复制
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>
    </div>

    <!-- 领取地址二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="领取地址"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="qr-code-content">
        <!-- 二维码区域 -->
        <div class="qr-code-section">
          <div class="qr-code-placeholder">
            <!-- 这里应该显示从后端获取的二维码图片 -->
            <el-image
              v-if="qrCodeUrl"
              :src="qrCodeUrl"
              fit="contain"
              style="width: 200px; height: 200px"
            >
              <template #error>
                <div class="image-slot">加载失败</div>
              </template>
            </el-image>
            <div v-else class="qr-code-mock">
              <div class="image-slot">暂无二维码</div>
            </div>
          </div>
        </div>

        <!-- 优惠券名称 -->
        <div class="coupon-name">
          <span class="label">优惠券名称：</span>
          <span class="value">{{ currentCoupon.name }}</span>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="qrCodeVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.coupon-management {
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;
  }

  .search-section {
    .search-form {
      :deep(.plus-search) {
        background-color: #fff;
      }
    }
  }

  .tab-button-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .coupon-tabs {
    flex: 1;

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__item) {
      font-size: 14px;
      font-weight: 500;
      color: #606266;

      &.is-active {
        color: #409eff;
        font-weight: 600;
      }

      &:hover {
        color: #409eff;
      }
    }
  }

  .button-section {
    display: flex;
    align-items: center;
    padding-top: 8px; // 微调对齐
  }

  .table-section {
    .operation-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .el-button {
        margin: 0;
        padding: 4px 8px;
      }
    }
  }

  // 领取地址弹窗样式
  :deep(.el-dialog) {
    .qr-code-content {
      text-align: center;

      .qr-code-section {
        margin-bottom: 20px;

        .qr-code-placeholder {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20px;

          .qr-code-mock {
            width: 200px;
            height: 200px;
            border: 2px solid #e4e7ed;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: #fff;
          }

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: var(--el-fill-color-light);
            color: var(--el-text-color-secondary);
            font-size: 14px;
          }
        }
      }

      .coupon-name {
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 14px;

        .label {
          color: #606266;
          margin-right: 8px;
        }

        .value {
          color: #303133;
          font-weight: 500;
        }
      }
    }

    .dialog-footer {
      text-align: center;
    }
  }
}
</style>
